/**
 * WCAG Rule 6: Focus Order - 2.4.3
 * 75% Automated - Manual review for complex layouts and logical flow
 */

import { Page } from 'puppeteer';
import { ManualReviewConfig, ManualReviewTemplate, ManualReviewItem } from '../utils/manual-review-template';
import { WcagEvidence } from '../types';

interface FocusableElement {
  selector: string;
  tagName: string;
  type?: string;
  tabIndex: number;
  position: { x: number; y: number; width: number; height: number };
  isVisible: boolean;
  ariaLabel?: string;
  text?: string;
}

export class FocusOrderCheck {
  private checkTemplate = new ManualReviewTemplate();

  /**
   * Perform focus order check - 75% automated
   */
  async performCheck(config: ManualReviewConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-006',
      'Focus Order',
      'operable',
      0.09,
      'A',
      0.75, // 75% automation rate
      config,
      this.executeFocusOrderCheck.bind(this)
    );
  }

  /**
   * Execute comprehensive focus order analysis
   */
  private async executeFocusOrderCheck(page: Page, config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Get all focusable elements
    const focusableElements = await this.getFocusableElements(page);
    
    // Analyze tab order
    const tabOrderAnalysis = await this.analyzeTabOrder(page, focusableElements);
    
    // Analyze visual layout correlation
    const layoutAnalysis = await this.analyzeLayoutCorrelation(focusableElements);
    
    // Check for skip links
    const skipLinkAnalysis = await this.analyzeSkipLinks(page);

    // Combine all analyses
    const allAnalyses = [tabOrderAnalysis, layoutAnalysis, skipLinkAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach(analysis => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedScore = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 75 // 75% automation as specified for Part 5
    };
  }

  /**
   * Get all focusable elements on the page
   */
  private async getFocusableElements(page: Page): Promise<FocusableElement[]> {
    return await page.evaluate(() => {
      const focusableSelectors = [
        'a[href]',
        'button:not([disabled])',
        'input:not([disabled]):not([type="hidden"])',
        'select:not([disabled])',
        'textarea:not([disabled])',
        '[tabindex]:not([tabindex="-1"])',
        'iframe',
        'object',
        'embed',
        'area[href]',
        'audio[controls]',
        'video[controls]',
        '[contenteditable="true"]',
      ];

      const elements: FocusableElement[] = [];
      
      focusableSelectors.forEach(selector => {
        const nodeList = document.querySelectorAll(selector);
        nodeList.forEach((element, index) => {
          const rect = element.getBoundingClientRect();
          const computedStyle = window.getComputedStyle(element);
          
          const isVisible = rect.width > 0 && 
                           rect.height > 0 && 
                           computedStyle.visibility !== 'hidden' && 
                           computedStyle.display !== 'none';

          if (isVisible) {
            elements.push({
              selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
              tagName: element.tagName.toLowerCase(),
              type: (element as HTMLInputElement).type || undefined,
              tabIndex: (element as HTMLElement).tabIndex,
              position: {
                x: rect.left,
                y: rect.top,
                width: rect.width,
                height: rect.height,
              },
              isVisible,
              ariaLabel: element.getAttribute('aria-label') || undefined,
              text: element.textContent?.trim().substring(0, 50) || undefined,
            });
          }
        });
      });

      return elements.sort((a, b) => {
        // Sort by tab index first, then by position
        if (a.tabIndex !== b.tabIndex) {
          if (a.tabIndex === 0 && b.tabIndex > 0) return 1;
          if (b.tabIndex === 0 && a.tabIndex > 0) return -1;
          return a.tabIndex - b.tabIndex;
        }
        
        // Then by vertical position (top to bottom)
        if (Math.abs(a.position.y - b.position.y) > 10) {
          return a.position.y - b.position.y;
        }
        
        // Then by horizontal position (left to right)
        return a.position.x - b.position.x;
      });
    });
  }

  /**
   * Analyze tab order sequence
   */
  private async analyzeTabOrder(page: Page, focusableElements: FocusableElement[]) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      let totalChecks = 1;
      let passedChecks = 0;

      // Check for custom tab indices
      const customTabIndices = focusableElements.filter(el => el.tabIndex > 0);
      
      if (customTabIndices.length > 0) {
        evidence.push({
          type: 'interaction',
          message: `Found ${customTabIndices.length} elements with custom tab indices`,
          element: 'various',
          details: {
            customTabIndices: customTabIndices.map(el => ({
              selector: el.selector,
              tabIndex: el.tabIndex,
            })),
          },
        });

        manualReviewItems.push({

          description: 'Review custom tab order implementation for logical flow',
          element: 'elements with tabindex > 0',
          priority: 'high',
          estimatedTime: 5,
          context: {
            customTabIndicesCount: customTabIndices.length,
            elements: customTabIndices,
          },
        });
      } else {
        passedChecks++;
        evidence.push({
          type: 'interaction',
          message: 'No custom tab indices found - using natural DOM order',
          element: 'page',
          details: {
            focusableElementsCount: focusableElements.length,
          },
        });
      }

      // Check for negative tab indices (should be excluded from tab order)
      const negativeTabIndices = await page.$$eval('[tabindex="-1"]', (elements) => {
        return elements.length;
      });

      if (negativeTabIndices > 0) {
        evidence.push({
          type: 'interaction',
          message: `Found ${negativeTabIndices} elements with tabindex="-1" (excluded from tab order)`,
          element: '[tabindex="-1"]',
          details: { count: negativeTabIndices },
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'interaction',
        message: 'Error analyzing tab order',
        element: 'page',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze tab order'],
        recommendations: ['Check tab order manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze correlation between visual layout and focus order
   */
  private analyzeLayoutCorrelation(focusableElements: FocusableElement[]) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      let totalChecks = 1;
      let passedChecks = 0;

      if (focusableElements.length < 2) {
        passedChecks++;
        evidence.push({
          type: 'interaction',
          message: 'Insufficient focusable elements for layout correlation analysis',
          element: 'page',
          details: { count: focusableElements.length },
        });

        return {
          totalChecks,
          passedChecks,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      // Group elements by approximate row (within 20px vertical tolerance)
      const rows: FocusableElement[][] = [];
      
      focusableElements.forEach(element => {
        const existingRow = rows.find(row => 
          Math.abs(row[0].position.y - element.position.y) <= 20
        );
        
        if (existingRow) {
          existingRow.push(element);
        } else {
          rows.push([element]);
        }
      });

      // Sort elements within each row by horizontal position
      rows.forEach(row => {
        row.sort((a, b) => a.position.x - b.position.x);
      });

      // Check for potential layout/focus order mismatches
      let layoutIssues = 0;
      
      for (let i = 0; i < focusableElements.length - 1; i++) {
        const current = focusableElements[i];
        const next = focusableElements[i + 1];
        
        // Check if next element is significantly to the left or above current
        const isLeftward = next.position.x < current.position.x - 50;
        const isUpward = next.position.y < current.position.y - 20;
        
        if (isLeftward || isUpward) {
          layoutIssues++;
          evidence.push({
            type: 'interaction',
            message: `Potential focus order issue: element ${i + 2} appears before element ${i + 1} visually`,
            element: next.selector,
            details: {
              currentElement: current,
              nextElement: next,
              isLeftward,
              isUpward,
            },
          });
        }
      }

      if (layoutIssues === 0) {
        passedChecks++;
        evidence.push({
          type: 'interaction',
          message: 'Focus order appears to follow visual layout',
          element: 'page',
          details: {
            focusableElementsCount: focusableElements.length,
            rowsCount: rows.length,
          },
        });
      } else {
        issues.push(`Found ${layoutIssues} potential focus order/layout mismatches`);
        recommendations.push('Review focus order to ensure it follows visual layout');
      }

      // Add manual review for complex layouts
      if (rows.length > 3 || focusableElements.length > 10) {
        manualReviewItems.push({

          description: 'Review focus order for complex layout with multiple rows/columns',
          element: 'page layout',
          priority: 'medium',
          estimatedTime: 7,
          context: {
            focusableElementsCount: focusableElements.length,
            rowsCount: rows.length,
            layoutIssues,
          },
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'interaction',
        message: 'Error analyzing layout correlation',
        element: 'page',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze layout correlation'],
        recommendations: ['Check layout correlation manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze skip links for navigation efficiency
   */
  private async analyzeSkipLinks(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const skipLinks = await page.$$eval('a[href^="#"]', (links) => {
        return links
          .map((link, index) => ({
            index,
            href: link.getAttribute('href') || '',
            text: link.textContent?.trim() || '',
            isVisible: link.offsetWidth > 0 && link.offsetHeight > 0,
            position: link.getBoundingClientRect(),
          }))
          .filter(link => {
            const text = link.text.toLowerCase();
            return text.includes('skip') || 
                   text.includes('jump') || 
                   text.includes('main') ||
                   text.includes('content') ||
                   text.includes('navigation');
          });
      });

      let totalChecks = 1;
      let passedChecks = 0;

      if (skipLinks.length > 0) {
        passedChecks++;
        evidence.push({
          type: 'interaction',
          message: `Found ${skipLinks.length} potential skip link(s)`,
          element: 'a[href^="#"]',
          details: {
            skipLinks: skipLinks.map(link => ({
              text: link.text,
              href: link.href,
              isVisible: link.isVisible,
            })),
          },
        });

        // Add manual review for skip link functionality
        manualReviewItems.push({

          description: 'Test skip link functionality and target validity',
          element: 'skip links',
          priority: 'medium',
          estimatedTime: 3,
          context: {
            skipLinksCount: skipLinks.length,
            skipLinks,
          },
        });
      } else {
        evidence.push({
          type: 'interaction',
          message: 'No skip links found - may impact navigation efficiency',
          element: 'page',
          details: { skipLinksFound: false },
        });

        recommendations.push('Consider adding skip links for improved navigation');
        
        manualReviewItems.push({

          description: 'Evaluate if skip links are needed for this page layout',
          element: 'page navigation',
          priority: 'low',
          estimatedTime: 2,
          context: {
            pageHasNavigation: true, // Assume true for manual review
          },
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'interaction',
        message: 'Error analyzing skip links',
        element: 'a[href^="#"]',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze skip links'],
        recommendations: ['Check skip links manually'],
        manualReviewItems,
      };
    }
  }
}
