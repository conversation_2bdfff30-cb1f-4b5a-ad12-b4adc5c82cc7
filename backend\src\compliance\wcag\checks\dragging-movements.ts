/**
 * WCAG Rule 13: Dragging Movements - 2.5.7
 * 70% Automated - Manual review for gesture alternatives and complex interactions
 */

import { Page } from 'puppeteer';
import { ManualReviewConfig, ManualReviewTemplate, ManualReviewItem } from '../utils/manual-review-template';
import { WcagEvidence } from '../types';

interface DragInteraction {
  element: string;
  type: 'drag-drop' | 'slider' | 'sortable' | 'resizable' | 'swipe';
  hasAlternative: boolean;
  alternativeMethod?: string;
  isEssential: boolean;
}

export class DraggingMovementsCheck {
  private checkTemplate = new ManualReviewTemplate();

  /**
   * Perform dragging movements check - 70% automated
   */
  async performCheck(config: ManualReviewConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-013',
      'Dragging Movements',
      'operable',
      0.06,
      'AA',
      0.70, // 70% automation rate
      config,
      this.executeDraggingMovementsCheck.bind(this)
    );
  }

  /**
   * Execute comprehensive dragging movements analysis
   */
  private async executeDraggingMovementsCheck(page: Page, config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Analyze drag and drop elements
    const dragDropAnalysis = await this.analyzeDragDropElements(page);
    
    // Analyze sliders and range inputs
    const sliderAnalysis = await this.analyzeSliders(page);
    
    // Analyze sortable lists
    const sortableAnalysis = await this.analyzeSortableLists(page);
    
    // Analyze resizable elements
    const resizableAnalysis = await this.analyzeResizableElements(page);
    
    // Check for touch gestures
    const gestureAnalysis = await this.analyzeGestureInteractions(page);

    // Combine all analyses
    const allAnalyses = [dragDropAnalysis, sliderAnalysis, sortableAnalysis, resizableAnalysis, gestureAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach(analysis => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedScore = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
    };
  }

  /**
   * Analyze drag and drop elements
   */
  private async analyzeDragDropElements(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const dragDropElements = await page.$$eval('[draggable="true"], .draggable, .ui-draggable', (elements) => {
        return elements.map((element, index) => {
          const rect = element.getBoundingClientRect();
          return {
            index,
            tagName: element.tagName.toLowerCase(),
            className: element.className,
            id: element.id,
            draggable: element.getAttribute('draggable'),
            hasKeyboardHandler: element.hasAttribute('onkeydown') || element.hasAttribute('onkeyup'),
            position: {
              x: rect.left,
              y: rect.top,
              width: rect.width,
              height: rect.height,
            },
            isVisible: rect.width > 0 && rect.height > 0,
          };
        });
      });

      let totalChecks = dragDropElements.length;
      let passedChecks = 0;

      if (dragDropElements.length === 0) {
        evidence.push({
          type: 'info',
          message: 'No drag and drop elements found',
          element: 'page',
          details: { dragDropElementsFound: false },
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      dragDropElements.forEach((element, index) => {
        if (element.hasKeyboardHandler) {
          passedChecks++;
          evidence.push({
            type: 'info',
            message: `Drag element ${index + 1} has keyboard handlers`,
            element: `${element.tagName}${element.id ? '#' + element.id : ''}`,
            details: {
              hasKeyboardHandler: true,
              className: element.className,
            },
          });
        } else {
          issues.push(`Drag element ${index + 1} lacks keyboard alternative`);
          evidence.push({
            type: 'warning',
            message: `Drag element ${index + 1} may lack keyboard alternative`,
            element: `${element.tagName}${element.id ? '#' + element.id : ''}`,
            details: {
              hasKeyboardHandler: false,
              className: element.className,
            },
          });
        }

        // Add manual review for each drag element
        manualReviewItems.push({
          type: 'drag_alternative',
          description: `Verify drag element ${index + 1} has functional keyboard/click alternative`,
          element: `${element.tagName}${element.id ? '#' + element.id : ''}`,
          priority: 'high',
          estimatedTime: 4,
          context: {
            elementIndex: index + 1,
            elementType: 'drag-drop',
            hasKeyboardHandler: element.hasKeyboardHandler,
            className: element.className,
          },
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing drag and drop elements',
        element: '[draggable]',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze drag and drop elements'],
        recommendations: ['Check drag and drop functionality manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze sliders and range inputs
   */
  private async analyzeSliders(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const sliders = await page.$$eval('input[type="range"], .slider, .ui-slider', (elements) => {
        return elements.map((element, index) => {
          const input = element as HTMLInputElement;
          return {
            index,
            type: input.type || 'slider',
            min: input.min || '',
            max: input.max || '',
            step: input.step || '',
            value: input.value || '',
            hasLabel: !!element.getAttribute('aria-label') || !!document.querySelector(`label[for="${element.id}"]`),
            hasKeyboardSupport: input.type === 'range', // Native range inputs have keyboard support
            className: element.className,
          };
        });
      });

      let totalChecks = sliders.length;
      let passedChecks = 0;

      if (sliders.length === 0) {
        evidence.push({
          type: 'info',
          message: 'No sliders found',
          element: 'page',
          details: { slidersFound: false },
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      sliders.forEach((slider, index) => {
        if (slider.type === 'range' || slider.hasKeyboardSupport) {
          passedChecks++;
          evidence.push({
            type: 'info',
            message: `Slider ${index + 1} has keyboard support`,
            element: `input[type="range"], .slider`,
            details: {
              type: slider.type,
              hasKeyboardSupport: true,
              hasLabel: slider.hasLabel,
            },
          });
        } else {
          issues.push(`Slider ${index + 1} may lack keyboard support`);
          evidence.push({
            type: 'warning',
            message: `Slider ${index + 1} requires keyboard support verification`,
            element: `.slider, .ui-slider`,
            details: {
              type: slider.type,
              className: slider.className,
            },
          });
        }

        // Add manual review for custom sliders
        if (slider.type !== 'range') {
          manualReviewItems.push({
            type: 'slider_keyboard_support',
            description: `Test custom slider ${index + 1} keyboard functionality (arrow keys, page up/down)`,
            element: `.slider, .ui-slider`,
            priority: 'high',
            estimatedTime: 3,
            context: {
              sliderIndex: index + 1,
              sliderType: slider.type,
              className: slider.className,
            },
          });
        }
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing sliders',
        element: 'input[type="range"], .slider',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze sliders'],
        recommendations: ['Check slider functionality manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze sortable lists
   */
  private async analyzeSortableLists(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const sortableLists = await page.$$eval('.sortable, .ui-sortable, [data-sortable]', (elements) => {
        return elements.map((element, index) => ({
          index,
          tagName: element.tagName.toLowerCase(),
          className: element.className,
          itemCount: element.children.length,
          hasAriaSort: element.hasAttribute('aria-sort'),
          hasKeyboardHandlers: element.hasAttribute('onkeydown') || element.hasAttribute('onkeyup'),
        }));
      });

      let totalChecks = sortableLists.length;
      let passedChecks = 0;

      if (sortableLists.length === 0) {
        evidence.push({
          type: 'info',
          message: 'No sortable lists found',
          element: 'page',
          details: { sortableListsFound: false },
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      sortableLists.forEach((list, index) => {
        // All sortable lists require manual review for alternatives
        manualReviewItems.push({
          type: 'sortable_alternative',
          description: `Verify sortable list ${index + 1} has keyboard/button alternative for reordering`,
          element: `.sortable, .ui-sortable`,
          priority: 'high',
          estimatedTime: 5,
          context: {
            listIndex: index + 1,
            itemCount: list.itemCount,
            hasAriaSort: list.hasAriaSort,
            hasKeyboardHandlers: list.hasKeyboardHandlers,
          },
        });

        evidence.push({
          type: 'warning',
          message: `Sortable list ${index + 1} requires manual verification of alternatives`,
          element: `.sortable, .ui-sortable`,
          details: {
            itemCount: list.itemCount,
            hasAriaSort: list.hasAriaSort,
            requiresManualCheck: true,
          },
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing sortable lists',
        element: '.sortable, .ui-sortable',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze sortable lists'],
        recommendations: ['Check sortable functionality manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze resizable elements
   */
  private async analyzeResizableElements(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const resizableElements = await page.$$eval('.resizable, .ui-resizable, [data-resizable]', (elements) => {
        return elements.map((element, index) => ({
          index,
          tagName: element.tagName.toLowerCase(),
          className: element.className,
          hasResizeHandles: element.querySelector('.ui-resizable-handle, .resize-handle') !== null,
        }));
      });

      let totalChecks = resizableElements.length;
      let passedChecks = 0;

      if (resizableElements.length === 0) {
        evidence.push({
          type: 'info',
          message: 'No resizable elements found',
          element: 'page',
          details: { resizableElementsFound: false },
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      resizableElements.forEach((element, index) => {
        // All resizable elements require manual review
        manualReviewItems.push({
          type: 'resizable_alternative',
          description: `Verify resizable element ${index + 1} has keyboard/input alternative for resizing`,
          element: `.resizable, .ui-resizable`,
          priority: 'medium',
          estimatedTime: 4,
          context: {
            elementIndex: index + 1,
            hasResizeHandles: element.hasResizeHandles,
            className: element.className,
          },
        });

        evidence.push({
          type: 'warning',
          message: `Resizable element ${index + 1} requires manual verification of alternatives`,
          element: `.resizable, .ui-resizable`,
          details: {
            hasResizeHandles: element.hasResizeHandles,
            requiresManualCheck: true,
          },
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing resizable elements',
        element: '.resizable, .ui-resizable',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze resizable elements'],
        recommendations: ['Check resizable functionality manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze gesture interactions
   */
  private async analyzeGestureInteractions(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const gestureElements = await page.evaluate(() => {
        const elements = document.querySelectorAll('*');
        const gestureElements: any[] = [];

        elements.forEach((element, index) => {
          const hasTouch = element.hasAttribute('ontouchstart') || 
                          element.hasAttribute('ontouchmove') || 
                          element.hasAttribute('ontouchend');
          
          const hasSwipe = element.className.includes('swipe') || 
                          element.hasAttribute('data-swipe');

          if (hasTouch || hasSwipe) {
            gestureElements.push({
              index,
              tagName: element.tagName.toLowerCase(),
              className: element.className,
              hasTouch,
              hasSwipe,
              hasClick: element.hasAttribute('onclick') || element.hasAttribute('onmousedown'),
            });
          }
        });

        return gestureElements;
      });

      let totalChecks = gestureElements.length;
      let passedChecks = 0;

      if (gestureElements.length === 0) {
        evidence.push({
          type: 'info',
          message: 'No gesture interactions detected',
          element: 'page',
          details: { gestureElementsFound: false },
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      gestureElements.forEach((element, index) => {
        if (element.hasClick) {
          passedChecks++;
          evidence.push({
            type: 'info',
            message: `Gesture element ${index + 1} has click alternative`,
            element: element.tagName,
            details: {
              hasTouch: element.hasTouch,
              hasSwipe: element.hasSwipe,
              hasClick: element.hasClick,
            },
          });
        } else {
          issues.push(`Gesture element ${index + 1} may lack click alternative`);
          evidence.push({
            type: 'warning',
            message: `Gesture element ${index + 1} may need click alternative`,
            element: element.tagName,
            details: {
              hasTouch: element.hasTouch,
              hasSwipe: element.hasSwipe,
              hasClick: element.hasClick,
            },
          });
        }

        // Add manual review for all gesture interactions
        manualReviewItems.push({
          type: 'gesture_alternative',
          description: `Test gesture element ${index + 1} for non-dragging alternatives`,
          element: element.tagName,
          priority: 'high',
          estimatedTime: 3,
          context: {
            elementIndex: index + 1,
            gestureType: element.hasSwipe ? 'swipe' : 'touch',
            hasClickAlternative: element.hasClick,
          },
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing gesture interactions',
        element: 'gesture elements',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze gesture interactions'],
        recommendations: ['Check gesture functionality manually'],
        manualReviewItems,
      };
    }
  }
}
