/**
 * WCAG Rule 2: Captions - 1.2.2
 * 80% Automated - Manual review for caption accuracy and synchronization
 */

import { Page } from 'puppeteer';
import { ManualReviewConfig, ManualReviewTemplate, ManualReviewItem } from '../utils/manual-review-template';
import { WcagEvidence } from '../types';

export class CaptionsCheck {
  private checkTemplate = new ManualReviewTemplate();

  /**
   * Perform captions check - 80% automated
   */
  async performCheck(config: ManualReviewConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-002',
      'Captions',
      'perceivable',
      0.08,
      'A',
      0.80, // 80% automation rate
      config,
      this.executeCaptionsCheck.bind(this)
    );
  }

  /**
   * Execute comprehensive captions analysis
   */
  private async executeCaptionsCheck(page: Page, config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Analyze video elements
    const videoAnalysis = await this.analyzeVideoElements(page);
    
    // Analyze audio elements
    const audioAnalysis = await this.analyzeAudioElements(page);
    
    // Check for caption files
    const captionFileAnalysis = await this.analyzeCaptionFiles(page);
    
    // Check for embedded captions
    const embeddedCaptionAnalysis = await this.analyzeEmbeddedCaptions(page);

    // Combine all analyses
    const allAnalyses = [videoAnalysis, audioAnalysis, captionFileAnalysis, embeddedCaptionAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach(analysis => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedScore = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
    };
  }

  /**
   * Analyze video elements for caption requirements
   */
  private async analyzeVideoElements(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const videoElements = await page.$$eval('video', (videos) => {
        return videos.map((video, index) => ({
          index,
          src: video.src || '',
          poster: video.poster || '',
          controls: video.hasAttribute('controls'),
          autoplay: video.hasAttribute('autoplay'),
          muted: video.hasAttribute('muted'),
          tracks: Array.from(video.querySelectorAll('track')).map(track => ({
            kind: track.getAttribute('kind') || '',
            src: track.getAttribute('src') || '',
            srclang: track.getAttribute('srclang') || '',
            label: track.getAttribute('label') || '',
            default: track.hasAttribute('default'),
          })),
          duration: video.duration || 0,
          hasAudio: !video.muted,
        }));
      });

      let totalChecks = videoElements.length;
      let passedChecks = 0;

      videoElements.forEach((video, index) => {
        const captionTracks = video.tracks.filter(track => 
          track.kind === 'captions' || track.kind === 'subtitles'
        );

        if (video.hasAudio && video.duration > 0) {
          if (captionTracks.length > 0) {
            passedChecks++;
            evidence.push({
              type: 'info',
              message: `Video ${index + 1} has ${captionTracks.length} caption track(s)`,
              element: `video[src="${video.src}"]`,
              details: {
                tracks: captionTracks,
                duration: video.duration,
              },
            });

            // Add manual review for caption accuracy
            manualReviewItems.push({
              type: 'caption_accuracy',
              description: `Verify caption accuracy and synchronization for video ${index + 1}`,
              element: `video[src="${video.src}"]`,
              priority: 'high',
              estimatedTime: 5,
              context: {
                videoSrc: video.src,
                captionTracks: captionTracks.length,
                duration: video.duration,
              },
            });
          } else {
            issues.push(`Video ${index + 1} with audio content lacks caption tracks`);
            evidence.push({
              type: 'error',
              message: `Video ${index + 1} requires captions for accessibility`,
              element: `video[src="${video.src}"]`,
              details: {
                hasAudio: video.hasAudio,
                duration: video.duration,
                captionTracks: 0,
              },
            });
            recommendations.push(`Add caption tracks to video ${index + 1}`);
          }
        } else {
          // Video without audio or zero duration
          passedChecks++;
          evidence.push({
            type: 'info',
            message: `Video ${index + 1} does not require captions (no audio or zero duration)`,
            element: `video[src="${video.src}"]`,
            details: {
              hasAudio: video.hasAudio,
              duration: video.duration,
            },
          });
        }
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing video elements for captions',
        element: 'video',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze video elements'],
        recommendations: ['Check video elements manually for caption requirements'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze audio elements for transcript requirements
   */
  private async analyzeAudioElements(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const audioElements = await page.$$eval('audio', (audios) => {
        return audios.map((audio, index) => ({
          index,
          src: audio.src || '',
          controls: audio.hasAttribute('controls'),
          autoplay: audio.hasAttribute('autoplay'),
          muted: audio.hasAttribute('muted'),
          duration: audio.duration || 0,
        }));
      });

      let totalChecks = audioElements.length;
      let passedChecks = 0;

      audioElements.forEach((audio, index) => {
        if (audio.duration > 0 && !audio.muted) {
          // Audio content requires transcript
          manualReviewItems.push({
            type: 'transcript_availability',
            description: `Verify transcript availability for audio ${index + 1}`,
            element: `audio[src="${audio.src}"]`,
            priority: 'high',
            estimatedTime: 3,
            context: {
              audioSrc: audio.src,
              duration: audio.duration,
            },
          });

          evidence.push({
            type: 'warning',
            message: `Audio ${index + 1} requires manual verification for transcript`,
            element: `audio[src="${audio.src}"]`,
            details: {
              duration: audio.duration,
              requiresTranscript: true,
            },
          });
        } else {
          passedChecks++;
          evidence.push({
            type: 'info',
            message: `Audio ${index + 1} does not require transcript (muted or zero duration)`,
            element: `audio[src="${audio.src}"]`,
            details: {
              duration: audio.duration,
              muted: audio.muted,
            },
          });
        }
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing audio elements for transcripts',
        element: 'audio',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze audio elements'],
        recommendations: ['Check audio elements manually for transcript requirements'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze caption files (WebVTT, SRT, etc.)
   */
  private async analyzeCaptionFiles(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const trackElements = await page.$$eval('track', (tracks) => {
        return tracks.map((track, index) => ({
          index,
          kind: track.getAttribute('kind') || '',
          src: track.getAttribute('src') || '',
          srclang: track.getAttribute('srclang') || '',
          label: track.getAttribute('label') || '',
          default: track.hasAttribute('default'),
        }));
      });

      let totalChecks = trackElements.length;
      let passedChecks = 0;

      for (const track of trackElements) {
        if (track.kind === 'captions' || track.kind === 'subtitles') {
          if (track.src) {
            passedChecks++;
            evidence.push({
              type: 'info',
              message: `Caption track found: ${track.label || track.srclang || 'unlabeled'}`,
              element: `track[src="${track.src}"]`,
              details: {
                kind: track.kind,
                language: track.srclang,
                label: track.label,
                isDefault: track.default,
              },
            });

            // Add manual review for caption file quality
            manualReviewItems.push({
              type: 'caption_file_quality',
              description: `Review caption file quality and format: ${track.src}`,
              element: `track[src="${track.src}"]`,
              priority: 'medium',
              estimatedTime: 3,
              context: {
                captionSrc: track.src,
                language: track.srclang,
                kind: track.kind,
              },
            });
          } else {
            issues.push(`Caption track ${track.index + 1} missing src attribute`);
            evidence.push({
              type: 'error',
              message: `Caption track ${track.index + 1} has no source file`,
              element: `track[kind="${track.kind}"]`,
              details: { kind: track.kind },
            });
          }
        }
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing caption files',
        element: 'track',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze caption files'],
        recommendations: ['Check caption files manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze embedded captions in video content
   */
  private async analyzeEmbeddedCaptions(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      // Check for common video platforms with embedded captions
      const embeddedVideos = await page.$$eval('iframe', (iframes) => {
        return iframes
          .map((iframe, index) => ({
            index,
            src: iframe.src || '',
            title: iframe.title || '',
          }))
          .filter(iframe => {
            const src = iframe.src.toLowerCase();
            return src.includes('youtube') || 
                   src.includes('vimeo') || 
                   src.includes('wistia') ||
                   src.includes('brightcove');
          });
      });

      let totalChecks = embeddedVideos.length;
      let passedChecks = 0;

      embeddedVideos.forEach((video, index) => {
        // Embedded videos require manual verification
        manualReviewItems.push({
          type: 'embedded_captions',
          description: `Verify embedded video captions: ${video.title || `Video ${index + 1}`}`,
          element: `iframe[src*="${video.src.split('/')[2]}"]`,
          priority: 'high',
          estimatedTime: 4,
          context: {
            videoSrc: video.src,
            platform: this.detectVideoPlatform(video.src),
            title: video.title,
          },
        });

        evidence.push({
          type: 'warning',
          message: `Embedded video requires manual caption verification: ${video.title || `Video ${index + 1}`}`,
          element: `iframe[src="${video.src}"]`,
          details: {
            platform: this.detectVideoPlatform(video.src),
            requiresManualCheck: true,
          },
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };

    } catch (error) {
      evidence.push({
        type: 'error',
        message: 'Error analyzing embedded captions',
        element: 'iframe',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze embedded captions'],
        recommendations: ['Check embedded videos manually for captions'],
        manualReviewItems,
      };
    }
  }

  /**
   * Detect video platform from URL
   */
  private detectVideoPlatform(src: string): string {
    const url = src.toLowerCase();
    if (url.includes('youtube')) return 'YouTube';
    if (url.includes('vimeo')) return 'Vimeo';
    if (url.includes('wistia')) return 'Wistia';
    if (url.includes('brightcove')) return 'Brightcove';
    return 'Unknown';
  }
}
