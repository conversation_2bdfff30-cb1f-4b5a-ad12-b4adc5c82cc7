/**
 * WCAG Compliance Orchestrator
 * Coordinates all WCAG compliance checks and manages scan execution
 */

import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import puppeteerExtra from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import {
  WcagScanModel,
  WcagAutomatedResultModel,
  ContrastAnalysisResult,
  FocusAnalysisResult,
  KeyboardAnalysisResult,
  WcagRuleId,
  ScanStatus
} from './types';
import { ColorAnalyzer } from './utils/color-analyzer';
import { FocusTracker } from './utils/focus-tracker';
import { KeyboardTester } from './utils/keyboard-tester';
import { LayoutAnalyzer } from './utils/layout-analyzer';
import { WCAG_AUTOMATED_RULES } from './constants';
import {
  ContrastMinimumCheck,
  FocusVisibleCheck,
  FocusNotObscuredMinimumCheck,
  FocusNotObscuredEnhancedCheck,
  FocusAppearanceCheck,
  TargetSizeCheck,
  NonTextContentCheck,
  InfoRelationshipsCheck,
  KeyboardCheck,
  ErrorIdentificationCheck,
  NameRoleValueCheck,
  RedundantEntryCheck,
  ImageAlternatives3Check,
  KeyboardFocus3Check,
  CaptionsCheck,
  FocusOrderCheck,
  DraggingMovementsCheck,
  ConsistentHelpCheck,
  TextWordingCheck,
  MotorCheck,
  PronunciationMeaningCheck
} from './checks';

// Configure Puppeteer with stealth plugin
puppeteerExtra.use(StealthPlugin());

export interface ScanConfiguration {
  url: string;
  waitForSelector?: string;
  timeout?: number;
  viewport?: {
    width: number;
    height: number;
  };
  userAgent?: string;
  enableJavaScript?: boolean;
  enableImages?: boolean;
}

export interface ScanProgress {
  scanId: string;
  status: ScanStatus;
  currentRule?: WcagRuleId;
  completedRules: WcagRuleId[];
  totalRules: number;
  progress: number;
  estimatedTimeRemaining?: number;
}

export class WcagOrchestrator {
  private browser: Browser | null = null;
  private page: Page | null = null;
  private scanStartTime: Date | null = null;

  /**
   * Initialize the orchestrator with browser setup
   */
  async initialize(): Promise<void> {
    try {
      this.browser = await puppeteerExtra.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });
      
      this.page = await this.browser.newPage();
      
      // Set default viewport
      await this.page.setViewport({
        width: 1920,
        height: 1080,
        deviceScaleFactor: 1
      });
      
      // Set user agent
      await this.page.setUserAgent(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      );
      
    } catch (error) {
      console.error('Failed to initialize WCAG orchestrator:', error);
      throw new Error('Orchestrator initialization failed');
    }
  }

  /**
   * Start a comprehensive WCAG compliance scan
   */
  async startScan(config: ScanConfiguration): Promise<string> {
    if (!this.browser || !this.page) {
      throw new Error('Orchestrator not initialized. Call initialize() first.');
    }

    this.scanStartTime = new Date();
    const scanId = this.generateScanId();
    
    try {
      // Create initial scan record
      const scan: Partial<WcagScanModel> = {
        id: scanId,
        target_url: config.url,
        scan_status: 'running',
        scan_timestamp: this.scanStartTime,
        total_automated_checks: WCAG_AUTOMATED_RULES.length,
        passed_automated_checks: 0,
        failed_automated_checks: 0,
        manual_review_items: 0
      };
      
      // TODO: Save scan to database
      console.log('Starting WCAG scan:', scan);
      
      // Configure page based on scan configuration
      await this.configurePage(config);
      
      // Navigate to target URL
      await this.page.goto(config.url, {
        waitUntil: 'networkidle2',
        timeout: config.timeout || 30000
      });
      
      // Wait for specific selector if provided
      if (config.waitForSelector) {
        await this.page.waitForSelector(config.waitForSelector, {
          timeout: config.timeout || 10000
        });
      }
      
      // Start automated checks
      await this.runAutomatedChecks(scanId);
      
      return scanId;
      
    } catch (error) {
      console.error('Error starting WCAG scan:', error);
      
      // Update scan status to failed
      // TODO: Update scan in database
      
      throw new Error(`Scan failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Configure page settings based on scan configuration
   */
  private async configurePage(config: ScanConfiguration): Promise<void> {
    if (!this.page) return;
    
    // Set viewport if specified
    if (config.viewport) {
      await this.page.setViewport({
        width: config.viewport.width,
        height: config.viewport.height,
        deviceScaleFactor: 1
      });
    }
    
    // Set user agent if specified
    if (config.userAgent) {
      await this.page.setUserAgent(config.userAgent);
    }
    
    // Configure JavaScript
    await this.page.setJavaScriptEnabled(config.enableJavaScript !== false);
    
    // Configure images
    if (config.enableImages === false) {
      await this.page.setRequestInterception(true);
      this.page.on('request', (request) => {
        if (request.resourceType() === 'image') {
          request.abort();
        } else {
          request.continue();
        }
      });
    }
  }

  /**
   * Run all automated WCAG checks
   */
  private async runAutomatedChecks(scanId: string): Promise<void> {
    if (!this.page) return;

    const automatedRules = WCAG_AUTOMATED_RULES;

    console.log(`Running ${automatedRules.length} automated checks...`);

    // Run color contrast analysis
    await this.runContrastAnalysis(scanId);

    // Run focus analysis
    await this.runFocusAnalysis(scanId);

    // Run keyboard analysis
    await this.runKeyboardAnalysis(scanId);

    // Run layout analysis
    await this.runLayoutAnalysis(scanId);

    // Run Part 3 fully automated checks
    await this.runPart3AutomatedChecks(scanId);

    // Run Part 5 high & medium automation checks
    await this.runPart5HighMediumChecks(scanId);

    console.log('Automated checks completed');
  }

  /**
   * Run color contrast analysis
   */
  private async runContrastAnalysis(scanId: string): Promise<void> {
    if (!this.page) return;
    
    try {
      console.log('Running contrast analysis...');
      
      // Get all text elements with their colors
      const textElements = await this.page.evaluate(() => {
        const elements: Array<{
          selector: string;
          text: string;
          color: string;
          backgroundColor: string;
          fontSize: string;
          fontWeight: string;
        }> = [];
        
        const textNodes = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div, a, button, label');
        
        textNodes.forEach((element, index) => {
          const computedStyle = window.getComputedStyle(element);
          const text = element.textContent?.trim();
          
          if (text && text.length > 0) {
            elements.push({
              selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
              text: text.substring(0, 100), // Limit text length
              color: computedStyle.color,
              backgroundColor: computedStyle.backgroundColor,
              fontSize: computedStyle.fontSize,
              fontWeight: computedStyle.fontWeight
            });
          }
        });
        
        return elements.slice(0, 50); // Limit to first 50 elements
      });
      
      // Analyze each text element
      for (const element of textElements) {
        const isLargeText = ColorAnalyzer.isLargeText(element.fontSize, element.fontWeight);
        const contrastResult = ColorAnalyzer.analyzeContrast(
          element.color,
          element.backgroundColor,
          isLargeText
        );
        
        // TODO: Save contrast analysis to database
        const analysis: ContrastAnalysisResult = {
          elementSelector: element.selector,
          elementType: 'text',
          foregroundColor: element.color,
          backgroundColor: element.backgroundColor,
          contrastRatio: contrastResult.ratio,
          isLargeText: isLargeText,
          levelAAPass: contrastResult.level !== 'FAIL',
          levelAAAPass: contrastResult.level === 'AAA',
          contextDescription: `Text element with ${element.text.substring(0, 50)}...`,
          recommendation: contrastResult.recommendation || 'No issues found'
        };
        
        console.log('Contrast analysis:', analysis);
      }
      
    } catch (error) {
      console.error('Error in contrast analysis:', error);
    }
  }

  /**
   * Run focus analysis
   */
  private async runFocusAnalysis(scanId: string): Promise<void> {
    if (!this.page) return;
    
    try {
      console.log('Running focus analysis...');
      
      const focusReport = await FocusTracker.generateFocusReport(this.page);
      
      // TODO: Save focus analysis to database
      console.log('Focus analysis:', focusReport);
      
    } catch (error) {
      console.error('Error in focus analysis:', error);
    }
  }

  /**
   * Run keyboard analysis
   */
  private async runKeyboardAnalysis(scanId: string): Promise<void> {
    if (!this.page) return;
    
    try {
      console.log('Running keyboard analysis...');
      
      const keyboardReport = await KeyboardTester.generateKeyboardReport(this.page);
      
      // TODO: Save keyboard analysis to database
      console.log('Keyboard analysis:', keyboardReport);
      
    } catch (error) {
      console.error('Error in keyboard analysis:', error);
    }
  }

  /**
   * Run layout analysis
   */
  private async runLayoutAnalysis(scanId: string): Promise<void> {
    if (!this.page) return;
    
    try {
      console.log('Running layout analysis...');
      
      const layoutReport = await LayoutAnalyzer.analyzePageLayout(this.page);
      
      // TODO: Save layout analysis to database
      console.log('Layout analysis:', layoutReport);
      
    } catch (error) {
      console.error('Error in layout analysis:', error);
    }
  }

  /**
   * Run Part 3 fully automated checks and Part 4 very high automation checks
   */
  private async runPart3AutomatedChecks(scanId: string): Promise<string> {
    if (!this.page) {
      throw new Error('Browser page not available');
    }

    console.log('🚀 Running Part 3 fully automated checks...');

    const checkConfig = {
      targetUrl: await this.page.url(),
      timeout: 30000,
      scanId,
      page: this.page,
    };

    // Config for Part 4 checks with manual review capabilities
    const manualReviewConfig = {
      ...checkConfig,
      enableManualTracking: true,
      maxManualItems: 50,
    };

    try {
      // Rule 4: Contrast Minimum
      console.log('Running Contrast Minimum check...');
      const contrastCheck = new ContrastMinimumCheck();
      const contrastResult = await contrastCheck.performCheck(checkConfig);
      console.log(`Contrast check completed: ${contrastResult.status}`);

      // Rule 7: Focus Visible
      console.log('Running Focus Visible check...');
      const focusVisibleCheck = new FocusVisibleCheck();
      const focusVisibleResult = await focusVisibleCheck.performCheck(checkConfig);
      console.log(`Focus Visible check completed: ${focusVisibleResult.status}`);

      // Rule 10: Focus Not Obscured Minimum
      console.log('Running Focus Not Obscured Minimum check...');
      const focusNotObscuredMinCheck = new FocusNotObscuredMinimumCheck();
      const focusNotObscuredMinResult = await focusNotObscuredMinCheck.performCheck(checkConfig);
      console.log(`Focus Not Obscured Minimum check completed: ${focusNotObscuredMinResult.status}`);

      // Rule 11: Focus Not Obscured Enhanced
      console.log('Running Focus Not Obscured Enhanced check...');
      const focusNotObscuredEnhCheck = new FocusNotObscuredEnhancedCheck();
      const focusNotObscuredEnhResult = await focusNotObscuredEnhCheck.performCheck(checkConfig);
      console.log(`Focus Not Obscured Enhanced check completed: ${focusNotObscuredEnhResult.status}`);

      // Rule 12: Focus Appearance
      console.log('Running Focus Appearance check...');
      const focusAppearanceCheck = new FocusAppearanceCheck();
      const focusAppearanceResult = await focusAppearanceCheck.performCheck(checkConfig);
      console.log(`Focus Appearance check completed: ${focusAppearanceResult.status}`);

      // Rule 14: Target Size
      console.log('Running Target Size check...');
      const targetSizeCheck = new TargetSizeCheck();
      const targetSizeResult = await targetSizeCheck.performCheck(checkConfig);
      console.log(`Target Size check completed: ${targetSizeResult.status}`);

      console.log('✅ Part 3 automated checks completed successfully');

      // Part 4: Very High Automation Checks (85-95% automation)
      console.log('🚀 Starting Part 4: Very High Automation checks...');

      // Rule 1: Non-text Content
      console.log('Running Non-text Content check...');
      const nonTextContentCheck = new NonTextContentCheck();
      const nonTextContentResult = await nonTextContentCheck.performCheck(manualReviewConfig);
      console.log(`Non-text Content check completed: ${nonTextContentResult.status}`);

      // Rule 3: Info and Relationships
      console.log('Running Info and Relationships check...');
      const infoRelationshipsCheck = new InfoRelationshipsCheck();
      const infoRelationshipsResult = await infoRelationshipsCheck.performCheck(manualReviewConfig);
      console.log(`Info and Relationships check completed: ${infoRelationshipsResult.status}`);

      // Rule 5: Keyboard
      console.log('Running Keyboard check...');
      const keyboardCheck = new KeyboardCheck();
      const keyboardResult = await keyboardCheck.performCheck(manualReviewConfig);
      console.log(`Keyboard check completed: ${keyboardResult.status}`);

      // Rule 8: Error Identification
      console.log('Running Error Identification check...');
      const errorIdentificationCheck = new ErrorIdentificationCheck();
      const errorIdentificationResult = await errorIdentificationCheck.performCheck(manualReviewConfig);
      console.log(`Error Identification check completed: ${errorIdentificationResult.status}`);

      // Rule 9: Name, Role, Value
      console.log('Running Name, Role, Value check...');
      const nameRoleValueCheck = new NameRoleValueCheck();
      const nameRoleValueResult = await nameRoleValueCheck.performCheck(manualReviewConfig);
      console.log(`Name, Role, Value check completed: ${nameRoleValueResult.status}`);

      // Rule 16: Redundant Entry
      console.log('Running Redundant Entry check...');
      const redundantEntryCheck = new RedundantEntryCheck();
      const redundantEntryResult = await redundantEntryCheck.performCheck(manualReviewConfig);
      console.log(`Redundant Entry check completed: ${redundantEntryResult.status}`);

      // Rule 17: Image Alternatives 3.0
      console.log('Running Image Alternatives 3.0 check...');
      const imageAlternatives3Check = new ImageAlternatives3Check();
      const imageAlternatives3Result = await imageAlternatives3Check.performCheck(manualReviewConfig);
      console.log(`Image Alternatives 3.0 check completed: ${imageAlternatives3Result.status}`);

      // Rule 19: Keyboard Focus 3.0
      console.log('Running Keyboard Focus 3.0 check...');
      const keyboardFocus3Check = new KeyboardFocus3Check();
      const keyboardFocus3Result = await keyboardFocus3Check.performCheck(manualReviewConfig);
      console.log(`Keyboard Focus 3.0 check completed: ${keyboardFocus3Result.status}`);

      console.log('✅ Part 4 very high automation checks completed successfully');

      // TODO: Store results in database
      // For now, just log the results
      const allResults = [
        contrastResult,
        focusVisibleResult,
        focusNotObscuredMinResult,
        focusNotObscuredEnhResult,
        focusAppearanceResult,
        targetSizeResult,
        nonTextContentResult,
        infoRelationshipsResult,
        keyboardResult,
        errorIdentificationResult,
        nameRoleValueResult,
        redundantEntryResult,
        imageAlternatives3Result,
        keyboardFocus3Result
      ];

      console.log('WCAG Check Results Summary (Parts 3 & 4):');
      allResults.forEach(result => {
        console.log(`- ${result.ruleName}: ${result.status} (${result.score}/${result.maxScore})`);
      });

      // Calculate overall statistics
      const totalChecks = allResults.length;
      const passedChecks = allResults.filter(r => r.status === 'passed').length;
      const overallScore = Math.round(allResults.reduce((sum, r) => sum + r.score, 0) / totalChecks);

      console.log(`\n📊 Overall Statistics:`);
      console.log(`- Total checks: ${totalChecks}`);
      console.log(`- Passed checks: ${passedChecks}`);
      console.log(`- Overall score: ${overallScore}%`);
      console.log(`- Part 3 (Fully Automated): 6 checks`);
      console.log(`- Part 4 (Very High Automation): 8 checks`);
      console.log(`- Total automation coverage: ${totalChecks} checks completed`);

      return scanId;

    } catch (error) {
      console.error('❌ Error running Part 3 automated checks:', error);
      throw error;
    }
  }

  /**
   * Run Part 5 high & medium automation checks (60-80% automation)
   */
  private async runPart5HighMediumChecks(scanId: string): Promise<string> {
    if (!this.page) {
      throw new Error('Browser page not available');
    }

    console.log('🚀 Starting Part 5: High & Medium Automation checks...');

    const manualReviewConfig = {
      targetUrl: await this.page.url(),
      timeout: 30000,
      scanId,
      page: this.page,
      enableManualTracking: true,
      maxManualItems: 50,
    };

    try {
      // Rule 2: Captions (80% automation)
      console.log('Running Captions check...');
      const captionsCheck = new CaptionsCheck();
      const captionsResult = await captionsCheck.performCheck(manualReviewConfig);
      console.log(`Captions check completed: ${captionsResult.status}`);

      // Rule 6: Focus Order (75% automation)
      console.log('Running Focus Order check...');
      const focusOrderCheck = new FocusOrderCheck();
      const focusOrderResult = await focusOrderCheck.performCheck(manualReviewConfig);
      console.log(`Focus Order check completed: ${focusOrderResult.status}`);

      // Rule 13: Dragging Movements (70% automation)
      console.log('Running Dragging Movements check...');
      const draggingMovementsCheck = new DraggingMovementsCheck();
      const draggingMovementsResult = await draggingMovementsCheck.performCheck(manualReviewConfig);
      console.log(`Dragging Movements check completed: ${draggingMovementsResult.status}`);

      // Rule 15: Consistent Help (80% automation)
      console.log('Running Consistent Help check...');
      const consistentHelpCheck = new ConsistentHelpCheck();
      const consistentHelpResult = await consistentHelpCheck.performCheck(manualReviewConfig);
      console.log(`Consistent Help check completed: ${consistentHelpResult.status}`);

      // Rule 18: Text and Wording (75% automation)
      console.log('Running Text and Wording check...');
      const textWordingCheck = new TextWordingCheck();
      const textWordingResult = await textWordingCheck.performCheck(manualReviewConfig);
      console.log(`Text and Wording check completed: ${textWordingResult.status}`);

      // Rule 20: Motor (80% automation)
      console.log('Running Motor check...');
      const motorCheck = new MotorCheck();
      const motorResult = await motorCheck.performCheck(manualReviewConfig);
      console.log(`Motor check completed: ${motorResult.status}`);

      // Rule 21: Pronunciation & Meaning (60% automation)
      console.log('Running Pronunciation & Meaning check...');
      const pronunciationMeaningCheck = new PronunciationMeaningCheck();
      const pronunciationMeaningResult = await pronunciationMeaningCheck.performCheck(manualReviewConfig);
      console.log(`Pronunciation & Meaning check completed: ${pronunciationMeaningResult.status}`);

      console.log('✅ Part 5 high & medium automation checks completed successfully');

      // Log Part 5 results
      const part5Results = [
        captionsResult,
        focusOrderResult,
        draggingMovementsResult,
        consistentHelpResult,
        textWordingResult,
        motorResult,
        pronunciationMeaningResult
      ];

      console.log('WCAG Part 5 Check Results Summary:');
      part5Results.forEach(result => {
        console.log(`- ${result.ruleName}: ${result.status} (${result.score}/${result.maxScore})`);
      });

      // Calculate Part 5 statistics
      const totalPart5Checks = part5Results.length;
      const passedPart5Checks = part5Results.filter(r => r.status === 'passed').length;
      const part5Score = Math.round(part5Results.reduce((sum, r) => sum + r.score, 0) / totalPart5Checks);

      console.log(`\n📊 Part 5 Statistics:`);
      console.log(`- Total Part 5 checks: ${totalPart5Checks}`);
      console.log(`- Passed Part 5 checks: ${passedPart5Checks}`);
      console.log(`- Part 5 average score: ${part5Score}%`);
      console.log(`- Part 5 automation level: 60-80% (High & Medium)`);

      return scanId;

    } catch (error) {
      console.error('❌ Error running Part 5 high & medium automation checks:', error);
      throw error;
    }
  }

  /**
   * Get scan progress
   */
  async getScanProgress(scanId: string): Promise<ScanProgress> {
    // TODO: Implement database lookup for scan progress
    return {
      scanId,
      status: 'running',
      completedRules: [],
      totalRules: WCAG_AUTOMATED_RULES.length,
      progress: 0
    };
  }

  /**
   * Generate unique scan ID
   */
  private generateScanId(): string {
    return `wcag_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    try {
      if (this.page) {
        await this.page.close();
        this.page = null;
      }
      
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  }
}
